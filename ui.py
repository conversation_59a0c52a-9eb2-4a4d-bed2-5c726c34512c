import sys
import traceback
import logging
import os
from PyQt6.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit, QVBoxLayout, QPushButton,
    QTextBrowser, QMessageBox, QHBoxLayout, QFrame, QProgressBar,
    QFileDialog, QMainWindow, QStatusBar, QGridLayout, QMenu, QMenuBar,
    QComboBox, QCompleter
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QUrl, QTimer
from PyQt6.QtGui import QIcon, QFont, QPixmap, QDesktopServices, QAction
import logic_bot
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By

# Import our new modules
from splash_screen import show_splash
from about_dialog import AboutDialog
from resource_path import resource_path

# Application version
APP_VERSION = "1.0.0"

# Estados brasileiros
ESTADOS_BRASILEIROS = [
    "Acre (AC)", "Alagoas (AL)", "Amapá (AP)", "Amazonas (AM)", "Bahia (BA)",
    "Ceará (CE)", "Distrito Federal (DF)", "Espírito Santo (ES)", "Goiás (GO)",
    "Maranhão (MA)", "Mato Grosso (MT)", "Mato Grosso do Sul (MS)", "Minas Gerais (MG)",
    "Pará (PA)", "Paraíba (PB)", "Paraná (PR)", "Pernambuco (PE)", "Piauí (PI)",
    "Rio de Janeiro (RJ)", "Rio Grande do Norte (RN)", "Rio Grande do Sul (RS)",
    "Rondônia (RO)", "Roraima (RR)", "Santa Catarina (SC)", "São Paulo (SP)",
    "Sergipe (SE)", "Tocantins (TO)"
]

# Bairros populares por estado (exemplos)
BAIRROS_POPULARES = {
    "São Paulo (SP)": [
        "Vila Madalena", "Pinheiros", "Moema", "Jardins", "Vila Olímpia", "Itaim Bibi",
        "Brooklin", "Morumbi", "Santana", "Tatuapé", "Mooca", "Liberdade", "Bela Vista",
        "Consolação", "Higienópolis", "Perdizes", "Pompeia", "Lapa", "Vila Leopoldina",
        "Campo Belo", "Saúde", "Ipiranga", "Vila Prudente", "Penha", "Tucuruvi"
    ],
    "Rio de Janeiro (RJ)": [
        "Copacabana", "Ipanema", "Leblon", "Barra da Tijuca", "Botafogo", "Flamengo",
        "Tijuca", "Vila Isabel", "Maracanã", "Grajaú", "Méier", "Cachambi", "Engenho Novo",
        "Todos os Santos", "Piedade", "Abolição", "Pilares", "Del Castilho", "Inhaúma",
        "Ramos", "Olaria", "Penha", "Brás de Pina", "Cordovil", "Parada de Lucas"
    ],
    "Minas Gerais (MG)": [
        "Savassi", "Funcionários", "Centro", "Lourdes", "Anchieta", "Sion", "Mangabeiras",
        "Buritis", "Belvedere", "Cidade Nova", "Pampulha", "São Pedro", "Santa Efigênia",
        "Floresta", "Lagoinha", "Carlos Prates", "Gutierrez", "Santo Antônio", "São Lucas",
        "Castelo", "Sagrada Família", "Coração de Jesus", "Santa Teresa", "Horto", "Caiçaras"
    ],
    "Bahia (BA)": [
        "Pelourinho", "Barra", "Ondina", "Rio Vermelho", "Pituba", "Itaigara", "Caminho das Árvores",
        "Iguatemi", "Patamares", "Stella Maris", "Flamengo", "Garcia", "Graça", "Vitória",
        "Corredor da Vitória", "Campo Grande", "Nazaré", "Federação", "Engenho Velho de Brotas",
        "Brotas", "Costa Azul", "Armação", "Piatã", "Itapuã", "Amaralina"
    ]
}

# Função para obter bairros de um estado
def obter_bairros_estado(estado):
    return BAIRROS_POPULARES.get(estado, [
        "Centro", "Zona Norte", "Zona Sul", "Zona Leste", "Zona Oeste",
        "Centro Histórico", "Cidade Nova", "Jardim", "Vila", "Bairro Novo"
    ])

class ThreadScraper(QThread):
    sinal_finalizado = pyqtSignal(list)
    sinal_erro = pyqtSignal(str)
    sinal_progresso = pyqtSignal(int, int, dict)
    sinal_log = pyqtSignal(str, str)  # tipo, mensagem
    sinal_verificar_cancelamento = pyqtSignal()
    sinal_cancelado = pyqtSignal(bool)

    def __init__(self, estado, bairro, palavra_chave, quantidade):
        super().__init__()
        self.estado = estado
        self.bairro = bairro
        self.palavra_chave = palavra_chave
        self.quantidade = quantidade
        self.cancelado = False

    def run(self):
        try:
            self.sinal_log.emit("info", "Iniciando o Chrome em modo headless...")

            # Configurar o mecanismo de verificação de cancelamento
            self.sinal_cancelado.connect(self.set_cancelado)

            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--log-level=3")  # Silencia mensagens de log do Chrome

            try:
                self.sinal_log.emit("info", "Instalando/atualizando o ChromeDriver...")
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e:
                self.sinal_erro.emit(f"Erro ao iniciar o Chrome: {str(e)}")
                return

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            self.sinal_log.emit("info", "Abrindo o Google Maps...")
            driver.get('https://www.google.com/maps/')

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            # Buscar por localização (estado + bairro)
            localizacao = f"{self.bairro}, {self.estado}"
            if not logic_bot.buscar_localizacao(driver, localizacao):
                self.sinal_erro.emit(f"Falha ao buscar a localização especificada: {localizacao}")
                driver.quit()
                return

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            # Buscar por palavra-chave
            if not logic_bot.buscar_palavra_chave(driver, self.palavra_chave):
                self.sinal_erro.emit(f"Não foram encontrados resultados para '{self.palavra_chave}' na região informada.")
                driver.quit()
                return

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            # Extrair clientes
            self.sinal_log.emit("info", f"Iniciando extração de {self.quantidade} leads...")
            clientes = logic_bot.extrair_clientes(driver, self.quantidade, self.atualizar_progresso)

            self.sinal_finalizado.emit(clientes)
        except Exception as e:
            error_msg = f"Erro inesperado: {str(e)}\n\n{traceback.format_exc()}"
            self.sinal_erro.emit(error_msg)
            logging.error(error_msg)
        finally:
            if 'driver' in locals():
                self.sinal_log.emit("info", "Fechando o navegador...")
                driver.quit()

    def set_cancelado(self, valor):
        """Define o estado de cancelamento"""
        self.cancelado = valor

    def atualizar_progresso(self, atual, total, cliente):
        self.sinal_progresso.emit(atual, total, cliente)
        self.sinal_log.emit("info", f"Capturado lead {atual}/{total}: {cliente['nome']}")

class AplicativoScraper(QMainWindow):
    def __init__(self):
        super().__init__()
        self.clientes_extraidos = []
        self.cancelar_captura = False
        self.iniciarUI()

    def closeEvent(self, event):
        """Manipula o evento de fechamento da janela"""
        if hasattr(self, 'thread_scraper') and self.thread_scraper.isRunning():
            reply = QMessageBox.question(
                self, 'Confirmar Saída',
                'Uma captura está em andamento. Deseja realmente sair?',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Atualizar flags de cancelamento
                self.cancelar_captura = True
                self.thread_scraper.cancelado = True

                # Mostrar mensagem de encerramento
                self.statusbar.showMessage("Encerrando aplicação...")
                self.adicionar_log("aviso", "Encerrando aplicação durante captura em andamento...")

                # Tentar encerrar a thread de forma limpa
                self.thread_scraper.quit()

                # Esperar até 3 segundos para a thread terminar
                if self.thread_scraper.wait(3000):
                    self.adicionar_log("info", "Thread de captura encerrada com sucesso.")
                else:
                    self.adicionar_log("aviso", "Não foi possível encerrar a thread de captura normalmente. Forçando encerramento.")
                    self.thread_scraper.terminate()

                # Aceitar o evento de fechamento
                event.accept()
            else:
                # Usuário cancelou o fechamento
                event.ignore()
        else:
            # Não há captura em andamento, pode fechar normalmente
            event.accept()

    def atualizar_bairros(self):
        """Atualiza a lista de bairros baseada no estado selecionado"""
        estado_selecionado = self.combo_estado.currentText()
        bairros = obter_bairros_estado(estado_selecionado)

        # Configurar o completer com os bairros do estado selecionado
        completer = QCompleter(bairros)
        completer.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.entrada_bairro.setCompleter(completer)

    def iniciarUI(self):
        # Configurações da janela principal
        self.setWindowTitle(f'Captura de Leads v{APP_VERSION}')
        self.setWindowIcon(QIcon(resource_path('logo.png')))

        # Configurar para tela cheia
        self.setWindowState(Qt.WindowState.WindowMaximized)

        # Criar menu
        self.criar_menu()

        # Widget central
        widget_central = QWidget()
        self.setCentralWidget(widget_central)

        # Layout principal
        layout_principal = QVBoxLayout(widget_central)
        layout_principal.setContentsMargins(20, 20, 20, 20)
        layout_principal.setSpacing(15)

        # Adicionar logo no topo - layout centralizado
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 10, 10, 10)
        header_layout.setSpacing(5)

        # Logo centralizado
        logo_label = QLabel()
        pixmap = QPixmap(resource_path('logo.png'))
        scaled_pixmap = pixmap.scaled(140, 140, Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)
        logo_label.setPixmap(scaled_pixmap)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(logo_label)

        # Subtítulo
        app_subtitle = QLabel("Captura de Leads do Google Maps")
        app_subtitle.setFont(QFont('Segoe UI', 14))
        app_subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        app_subtitle.setStyleSheet("color: #495057; font-weight: 500;")
        header_layout.addWidget(app_subtitle)

        layout_principal.addWidget(header_frame)

        # Frame de entrada de dados
        frame_entrada = QFrame()
        frame_entrada.setObjectName("frameEntrada")
        layout_entrada = QGridLayout(frame_entrada)
        layout_entrada.setContentsMargins(20, 20, 20, 20)
        layout_entrada.setSpacing(25)  # Aumentado ainda mais o espaçamento entre os elementos

        # Estado
        label_estado = QLabel('Estado:')
        label_estado.setMinimumWidth(120)  # Largura mínima para o rótulo
        self.combo_estado = QComboBox()
        self.combo_estado.setMinimumHeight(35)  # Altura mínima para o campo
        self.combo_estado.addItems(ESTADOS_BRASILEIROS)
        self.combo_estado.setCurrentText("São Paulo (SP)")  # Estado padrão
        self.combo_estado.currentTextChanged.connect(self.atualizar_bairros)
        layout_entrada.addWidget(label_estado, 0, 0)
        layout_entrada.addWidget(self.combo_estado, 0, 1)

        # Bairro
        label_bairro = QLabel('Bairro:')
        label_bairro.setMinimumWidth(120)  # Largura mínima para o rótulo
        self.entrada_bairro = QLineEdit()
        self.entrada_bairro.setMinimumHeight(35)  # Altura mínima para o campo
        self.entrada_bairro.setPlaceholderText("Digite o bairro (ex: Vila Madalena)")
        layout_entrada.addWidget(label_bairro, 1, 0)
        layout_entrada.addWidget(self.entrada_bairro, 1, 1)

        # Configurar autocomplete inicial para São Paulo
        self.atualizar_bairros()

        # Palavra-chave
        label_palavra_chave = QLabel('Palavra-chave:')
        label_palavra_chave.setMinimumWidth(120)  # Largura mínima para o rótulo
        self.entrada_palavra_chave = QLineEdit()
        self.entrada_palavra_chave.setMinimumHeight(35)  # Altura mínima para o campo
        self.entrada_palavra_chave.setPlaceholderText("Digite o que deseja buscar (ex: Restaurantes)")
        layout_entrada.addWidget(label_palavra_chave, 2, 0)
        layout_entrada.addWidget(self.entrada_palavra_chave, 2, 1)

        # Quantidade
        label_quantidade = QLabel('Número de Leads:')
        label_quantidade.setMinimumWidth(120)  # Largura mínima para o rótulo
        self.entrada_quantidade = QLineEdit()
        self.entrada_quantidade.setMinimumHeight(35)  # Altura mínima para o campo
        self.entrada_quantidade.setPlaceholderText("Digite a quantidade desejada")
        layout_entrada.addWidget(label_quantidade, 3, 0)
        layout_entrada.addWidget(self.entrada_quantidade, 3, 1)

        # Adicionar espaço entre os campos e os botões
        layout_entrada.setRowMinimumHeight(4, 20)  # Aumentado o espaço antes dos botões

        # Botões de ação
        botoes_layout = QHBoxLayout()
        botoes_layout.setSpacing(15)  # Espaçamento entre os botões

        # Botão iniciar
        self.botao_iniciar = QPushButton('Iniciar Captura')
        self.botao_iniciar.setObjectName("botaoPrincipal")
        self.botao_iniciar.setMinimumHeight(40)  # Altura mínima para o botão
        self.botao_iniciar.clicked.connect(self.iniciar_scraping)
        botoes_layout.addWidget(self.botao_iniciar)

        # Botão cancelar
        self.botao_cancelar = QPushButton('Cancelar')
        self.botao_cancelar.setObjectName("botaoCancelar")
        self.botao_cancelar.setMinimumHeight(40)  # Altura mínima para o botão
        self.botao_cancelar.clicked.connect(self.cancelar_scraping)
        self.botao_cancelar.setEnabled(False)
        botoes_layout.addWidget(self.botao_cancelar)

        # Adicionar os botões ao layout com espaçamento
        layout_entrada.addLayout(botoes_layout, 5, 0, 1, 2)  # Mudado para linha 5 para dar espaço

        layout_principal.addWidget(frame_entrada)

        # Barra de progresso
        progresso_frame = QFrame()
        progresso_frame.setObjectName("progressoFrame")
        progresso_layout = QVBoxLayout(progresso_frame)

        self.barra_progresso = QProgressBar()
        self.barra_progresso.setFormat(" %p% - %v/%m leads")
        self.barra_progresso.setTextVisible(True)
        progresso_layout.addWidget(self.barra_progresso)

        layout_principal.addWidget(progresso_frame)

        # Resultados
        resultados_frame = QFrame()
        resultados_frame.setObjectName("resultadosFrame")
        resultados_layout = QVBoxLayout(resultados_frame)

        label_resultados = QLabel('Leads encontrados:')
        label_resultados.setObjectName("labelResultados")
        resultados_layout.addWidget(label_resultados)

        self.navegador_resultados = QTextBrowser()
        resultados_layout.addWidget(self.navegador_resultados)

        layout_principal.addWidget(resultados_frame, 1)  # Stretch factor para ocupar espaço disponível

        # Botões de exportação - com container para centralizar
        botao_exportar_container = QHBoxLayout()

        # Botão exportar para Excel
        self.botao_exportar_excel = QPushButton('📊 Exportar para Excel')
        self.botao_exportar_excel.setObjectName("botaoExportarExcel")
        self.botao_exportar_excel.setIcon(QIcon.fromTheme("document-save"))
        self.botao_exportar_excel.setMinimumHeight(40)  # Altura mínima para o botão
        self.botao_exportar_excel.setMinimumWidth(200)  # Largura mínima para o botão
        self.botao_exportar_excel.clicked.connect(self.exportar_para_excel)
        self.botao_exportar_excel.setEnabled(False)

        # Botão exportar para CSV
        self.botao_exportar_csv = QPushButton('📄 Exportar para CSV')
        self.botao_exportar_csv.setObjectName("botaoExportarCsv")
        self.botao_exportar_csv.setIcon(QIcon.fromTheme("document-save"))
        self.botao_exportar_csv.setMinimumHeight(40)  # Altura mínima para o botão
        self.botao_exportar_csv.setMinimumWidth(200)  # Largura mínima para o botão
        self.botao_exportar_csv.clicked.connect(self.exportar_para_csv)
        self.botao_exportar_csv.setEnabled(False)

        botao_exportar_container.addStretch()
        botao_exportar_container.addWidget(self.botao_exportar_excel)
        botao_exportar_container.addSpacing(10)  # Espaçamento entre os botões
        botao_exportar_container.addWidget(self.botao_exportar_csv)
        botao_exportar_container.addStretch()

        layout_principal.addLayout(botao_exportar_container)

        # Barra de status
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        self.statusbar.showMessage("Pronto")

        # Aplicar estilos
        self.aplicar_estilos()

    def aplicar_estilos(self):
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }

            #labelResultados {
                font-size: 16px;
                color: #343a40;
                font-weight: bold;
                padding-top: 10px;
            }
            QLineEdit {
                padding: 10px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                margin-bottom: 5px;
                font-size: 13px;
                min-height: 20px;
            }
            QComboBox {
                padding: 10px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                color: #495057;
                margin-bottom: 5px;
                font-size: 13px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: #f8f9fa;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                color: #495057;
                border: 1px solid #ced4da;
                border-radius: 6px;
                selection-background-color: #e3f2fd;
                selection-color: #1976d2;
                padding: 5px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f5f5f5;
                color: #333;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QLabel {
                color: #495057;
                font-weight: 500;
                margin-bottom: 5px;
                padding: 2px;
                font-size: 13px;
            }
            QPushButton {
                background-color: #4dabf7;
                color: white;
                padding: 12px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #339af0;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #adb5bd;
            }
            #botaoPrincipal {
                background-color: #37b24d;
                font-size: 15px;
                padding: 14px;
            }
            #botaoPrincipal:hover {
                background-color: #2f9e44;
            }
            #botaoCancelar {
                background-color: #e03131;
                font-size: 15px;
                padding: 14px;
            }
            #botaoCancelar:hover {
                background-color: #c92a2a;
            }
            #botaoExportarExcel {
                background-color: #364fc7;
            }
            #botaoExportarExcel:hover {
                background-color: #304aca;
            }
            #botaoExportarCsv {
                background-color: #37b24d;
            }
            #botaoExportarCsv:hover {
                background-color: #2f9e44;
            }
            QTextBrowser {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                font-size: 13px;
            }
            QProgressBar {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                text-align: center;
                background-color: #e9ecef;
                color: #495057;
                height: 25px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #37b24d;
                border-radius: 5px;
            }
            #headerFrame {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
                border: 1px solid #dee2e6;
                margin-bottom: 10px;
            }
            #frameEntrada, #progressoFrame, #resultadosFrame {
                background-color: white;
                border-radius: 8px;
                padding: 5px;
                border: 1px solid #dee2e6;
            }
            QStatusBar {
                background-color: #f1f3f5;
                color: #495057;
            }
        """)

    def criar_menu(self):
        """Cria a barra de menu do aplicativo"""
        menubar = self.menuBar()

        # Menu Arquivo
        menu_arquivo = menubar.addMenu('&Arquivo')

        # Ação Exportar Excel
        acao_exportar_excel = QAction('&Exportar para Excel', self)
        acao_exportar_excel.setShortcut('Ctrl+E')
        acao_exportar_excel.setStatusTip('Exportar leads capturados para Excel')
        acao_exportar_excel.triggered.connect(self.exportar_para_excel)
        menu_arquivo.addAction(acao_exportar_excel)

        # Ação Exportar CSV
        acao_exportar_csv = QAction('&Exportar para CSV', self)
        acao_exportar_csv.setShortcut('Ctrl+Shift+E')
        acao_exportar_csv.setStatusTip('Exportar leads capturados para CSV')
        acao_exportar_csv.triggered.connect(self.exportar_para_csv)
        menu_arquivo.addAction(acao_exportar_csv)

        menu_arquivo.addSeparator()

        # Ação Sair
        acao_sair = QAction('&Sair', self)
        acao_sair.setShortcut('Ctrl+Q')
        acao_sair.setStatusTip('Sair do aplicativo')
        acao_sair.triggered.connect(self.close)
        menu_arquivo.addAction(acao_sair)

        # Menu Ajuda
        menu_ajuda = menubar.addMenu('&Ajuda')

        # Ação Sobre
        acao_sobre = QAction('&Sobre', self)
        acao_sobre.setStatusTip('Informações sobre o aplicativo')
        acao_sobre.triggered.connect(self.mostrar_sobre)
        menu_ajuda.addAction(acao_sobre)

    def mostrar_sobre(self):
        """Exibe o diálogo Sobre"""
        dialog = AboutDialog(self)
        dialog.exec()

    def verificar_cancelamento(self):
        """Verifica se o usuário solicitou o cancelamento da operação"""
        if self.cancelar_captura:
            self.thread_scraper.sinal_cancelado.emit(True)
        else:
            self.thread_scraper.sinal_cancelado.emit(False)

    def cancelar_scraping(self):
        """Cancela a operação de scraping em andamento"""
        if hasattr(self, 'thread_scraper') and self.thread_scraper.isRunning():
            reply = QMessageBox.question(
                self, 'Confirmar Cancelamento',
                'Deseja realmente cancelar a captura em andamento?',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Atualizar flags de cancelamento
                self.cancelar_captura = True
                self.thread_scraper.cancelado = True

                # Atualizar interface
                self.statusbar.showMessage("Cancelando captura...")

                # Atualizar estado dos botões
                self.botao_cancelar.setEnabled(False)
                self.botao_iniciar.setEnabled(False)  # Desabilitar até que o cancelamento seja concluído

                # Verificar se há resultados parciais para habilitar exportação
                if len(self.clientes_extraidos) > 0:
                    self.botao_exportar_excel.setEnabled(True)
                    self.botao_exportar_csv.setEnabled(True)

                # Definir um timer para habilitar o botão iniciar após um tempo
                QTimer.singleShot(3000, lambda: self.botao_iniciar.setEnabled(True))



    def iniciar_scraping(self):
        # Verificar se já existe uma captura em andamento
        if hasattr(self, 'thread_scraper') and self.thread_scraper.isRunning():
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Captura em Andamento')
            msg_box.setText('Já existe uma captura em andamento. Por favor, aguarde ou cancele a operação atual.')
            msg_box.exec()
            return

        # Obter valores dos campos
        estado = self.combo_estado.currentText().strip()
        bairro = self.entrada_bairro.text().strip()
        palavra_chave = self.entrada_palavra_chave.text().strip()

        # Validação do estado
        if not estado:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Campo Obrigatório')
            msg_box.setText('Por favor, selecione um estado')
            msg_box.exec()
            self.combo_estado.setFocus()
            return

        # Validação do bairro
        if not bairro:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Campo Obrigatório')
            msg_box.setText('Por favor, insira um bairro')
            msg_box.exec()
            self.entrada_bairro.setFocus()
            return

        # Validação da palavra-chave
        if not palavra_chave:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Campo Obrigatório')
            msg_box.setText('Por favor, insira uma palavra-chave para busca')
            msg_box.exec()
            self.entrada_palavra_chave.setFocus()
            return

        try:
            quantidade = int(self.entrada_quantidade.text())
            if quantidade <= 0:
                raise ValueError("Quantidade deve ser maior que zero")
        except ValueError:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Entrada Inválida')
            msg_box.setText('Por favor, insira um número válido de leads')
            msg_box.exec()
            return

        self.botao_iniciar.setEnabled(False)
        self.botao_cancelar.setEnabled(True)
        self.botao_exportar_excel.setEnabled(False)
        self.botao_exportar_csv.setEnabled(False)
        self.navegador_resultados.clear()
        self.barra_progresso.setValue(0)
        self.barra_progresso.setMaximum(quantidade)
        self.statusbar.showMessage(f"Iniciando captura: {estado}, {bairro}, {palavra_chave}")
        self.cancelar_captura = False

        self.thread_scraper = ThreadScraper(estado, bairro, palavra_chave, quantidade)
        self.thread_scraper.sinal_finalizado.connect(self.scraping_finalizado)
        self.thread_scraper.sinal_erro.connect(self.erro_scraping)
        self.thread_scraper.sinal_progresso.connect(self.atualizar_progresso)
        self.thread_scraper.sinal_verificar_cancelamento.connect(self.verificar_cancelamento)
        self.thread_scraper.start()

    def scraping_finalizado(self, clientes):
        # Atualizar a lista de clientes extraídos
        self.clientes_extraidos = clientes

        # Atualizar estado dos botões
        self.botao_iniciar.setEnabled(True)
        self.botao_cancelar.setEnabled(False)
        self.botao_exportar_excel.setEnabled(len(clientes) > 0)
        self.botao_exportar_csv.setEnabled(len(clientes) > 0)

        # Atualizar interface
        total_clientes = len(clientes)
        if total_clientes > 0:
            msg = f"Captura concluída! {total_clientes} leads encontrados."
            self.statusbar.showMessage(msg)

            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setWindowTitle('Captura Concluída')
            msg_box.setText(f'A captura foi concluída com sucesso! {total_clientes} leads encontrados.')
            msg_box.exec()
        else:
            msg = "Captura concluída sem resultados."
            self.statusbar.showMessage(msg)

            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setWindowTitle('Sem Resultados')
            msg_box.setText('A captura foi concluída, mas nenhum lead foi encontrado.')
            msg_box.exec()

    def erro_scraping(self, erro):
        # Restaurar estado dos botões
        self.botao_iniciar.setEnabled(True)
        self.botao_cancelar.setEnabled(False)
        self.botao_exportar_excel.setEnabled(len(self.clientes_extraidos) > 0)
        self.botao_exportar_csv.setEnabled(len(self.clientes_extraidos) > 0)

        # Atualizar interface
        self.statusbar.showMessage("Erro na captura")

        # Exibir mensagem de erro para o usuário
        erro_resumido = erro.split('\n')[0] if '\n' in erro else erro
        msg_box = QMessageBox(self)
        msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle('Erro na Captura')
        msg_box.setText(f'Ocorreu um erro durante a captura:\n\n{erro_resumido}')
        msg_box.exec()

    def atualizar_progresso(self, atual, total, cliente):
        self.barra_progresso.setValue(atual)
        porcentagem = int((atual / total) * 100)

        # Formata o resultado de forma mais estruturada e legível
        resultado_html = f"""
        <div style="margin-bottom: 10px; padding: 5px; border-left: 3px solid #37b24d;">
            <b style="color: #343a40; font-size: 14px;">{cliente['nome']}</b><br>
            <span style="color: #495057;">Telefone: {cliente['telefone']}</span>
            {f'<br><span style="color: #495057;">Endereço: {cliente["endereco"]}</span>' if cliente.get('endereco') != "Endereço não disponível" else ''}
            {f'<br><span style="color: #495057;">Site: <a href="{cliente["site"]}">{cliente["site"]}</a></span>' if cliente.get('site') != "Site não disponível" else ''}
        </div>
        """
        self.navegador_resultados.append(resultado_html)
        self.statusbar.showMessage(f"Capturando leads: {atual} de {total} ({porcentagem}%)")

    def exportar_para_excel(self):
        """Exporta os dados para Excel no formato estilizado"""
        if not self.clientes_extraidos:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Sem Dados')
            msg_box.setText('Não há dados para exportar.')
            msg_box.exec()
            return

        file_dialog = QFileDialog(self)
        file_dialog.setWindowIcon(QIcon(resource_path('logo.png')))
        file_dialog.setWindowTitle("Salvar Arquivo Excel")
        file_dialog.setNameFilter("Arquivos Excel (*.xlsx);;Todos os Arquivos (*)")
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        file_dialog.setDefaultSuffix("xlsx")

        if file_dialog.exec():
            nome_arquivo = file_dialog.selectedFiles()[0]

            if nome_arquivo:
                if not nome_arquivo.endswith('.xlsx'):
                    nome_arquivo += '.xlsx'

                try:
                    # Obter o estado atual da busca
                    estado = self.combo_estado.currentText()

                    # Usar a nova função de exportação estilizada
                    logic_bot.salvar_clientes_estilizado(self.clientes_extraidos, nome_arquivo, estado, 'excel')
                    self.statusbar.showMessage(f"Dados exportados para {nome_arquivo}")

                    msg_box = QMessageBox(self)
                    msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
                    msg_box.setIcon(QMessageBox.Icon.Information)
                    msg_box.setWindowTitle('Exportação Concluída')
                    msg_box.setText(f'Dados exportados com sucesso para:\n{nome_arquivo}\n\nColunas: NOME, NUMERO, EMAIL, ESTADO')
                    msg_box.exec()
                except Exception as e:
                    self.statusbar.showMessage(f"Erro ao exportar: {str(e)}")

                    msg_box = QMessageBox(self)
                    msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
                    msg_box.setIcon(QMessageBox.Icon.Critical)
                    msg_box.setWindowTitle('Erro na Exportação')
                    msg_box.setText(f'Ocorreu um erro ao exportar os dados:\n\n{str(e)}')
                    msg_box.exec()

    def exportar_para_csv(self):
        """Exporta os dados para CSV no formato estilizado"""
        if not self.clientes_extraidos:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Sem Dados')
            msg_box.setText('Não há dados para exportar.')
            msg_box.exec()
            return

        file_dialog = QFileDialog(self)
        file_dialog.setWindowIcon(QIcon(resource_path('logo.png')))
        file_dialog.setWindowTitle("Salvar Arquivo CSV")
        file_dialog.setNameFilter("Arquivos CSV (*.csv);;Todos os Arquivos (*)")
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        file_dialog.setDefaultSuffix("csv")

        if file_dialog.exec():
            nome_arquivo = file_dialog.selectedFiles()[0]

            if nome_arquivo:
                if not nome_arquivo.endswith('.csv'):
                    nome_arquivo += '.csv'

                try:
                    # Obter o estado atual da busca
                    estado = self.combo_estado.currentText()

                    # Usar a nova função de exportação estilizada
                    logic_bot.salvar_clientes_estilizado(self.clientes_extraidos, nome_arquivo, estado, 'csv')
                    self.statusbar.showMessage(f"Dados exportados para {nome_arquivo}")

                    msg_box = QMessageBox(self)
                    msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
                    msg_box.setIcon(QMessageBox.Icon.Information)
                    msg_box.setWindowTitle('Exportação Concluída')
                    msg_box.setText(f'Dados exportados com sucesso para:\n{nome_arquivo}\n\nColunas: NOME, NUMERO, EMAIL, ESTADO')
                    msg_box.exec()
                except Exception as e:
                    self.statusbar.showMessage(f"Erro ao exportar: {str(e)}")

                    msg_box = QMessageBox(self)
                    msg_box.setWindowIcon(QIcon(resource_path('logo.png')))
                    msg_box.setIcon(QMessageBox.Icon.Critical)
                    msg_box.setWindowTitle('Erro na Exportação')
                    msg_box.setText(f'Ocorreu um erro ao exportar os dados:\n\n{str(e)}')
                    msg_box.exec()

    def exportar_para_xlsx(self):
        """Método legado - redireciona para o novo método Excel"""
        self.exportar_para_excel()

def main():
    app = QApplication(sys.argv)

    # Definir fonte global
    app.setFont(QFont('Segoe UI', 10))

    # Aplicar tema global
    app.setStyle('Fusion')

    # Configurar ícone do aplicativo globalmente
    app_icon = QIcon('logo.png')
    app.setWindowIcon(app_icon)

    # Mostrar splash screen e iniciar aplicativo após o splash
    show_splash(app, 2000)  # 2 segundos

    # Iniciar aplicativo principal
    ex = AplicativoScraper()

    # Função para mostrar maximizado após o splash
    def mostrar_maximizado():
        ex.show()
        ex.showMaximized()  # Força maximização após mostrar

    # Mostrar a janela principal maximizada após um pequeno atraso
    QTimer.singleShot(2100, mostrar_maximizado)

    sys.exit(app.exec())

if __name__ == '__main__':
    main()
